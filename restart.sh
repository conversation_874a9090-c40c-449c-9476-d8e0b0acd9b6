#!/bin/bash
echo "go build"
go mod tidy
go build -o go-admin main.go
chmod +x ./go-admin
echo "kill go-admin service"
pid=`ps -aux|grep go-admin | grep -v grep | awk '{print $2}'`
echo "kill $pid"
if [ -z $pid ]; then
	echo "go-admin not running"
else
	kill -9 $pid # kill go-admin service
	echo "kill go-admin pid:$pid"
fi

nohup ./go-admin server -c=config/settings.yml >> temp/logs/access.log 2>&1 & #后台启动服务将日志写入access.log文件
echo "run go-admin success"
ps -aux | grep go-admin
