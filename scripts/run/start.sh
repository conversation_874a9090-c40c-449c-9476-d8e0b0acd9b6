#!/bin/bash

notRunFlag="0"

pStart="start"
pStop="stop"
pRestart="restart"


function isProcessRun() {
  pid=`ps -ef|grep platformV2 | grep -v grep  | awk '{print $2}'`
  if [ -z "$pid" ]
  then
    return $notRunFlag
  else
    return $pid
  fi
}

function start() {
  isProcessRun
  if [ "$?" = "$notRunFlag" ]
  then
    echo "当前进程没有启动"
  else
    echo "当前进程已经启动不需要启动"
    return
  fi

  startProcess
  sleep 2
  isProcessRun
  if [ "$?" = "$notRunFlag" ]
  then
    echo "进程启动失败"
  fi
}

function stopProcess() {
  isProcessRun
  if [ "$?" = "$notRunFlag" ]
  then
     echo "进程没有在运行"
  else
     `ps -ef|grep platformV2 | grep -v grep  | awk '{print $2}'|xargs kill`
  fi
  isProcessRun
  sleep 3
  if [ "$?" = "$notRunFlag" ]
  then
    echo "进程关闭成功"
  else
    echo "进程关闭失败"
  fi
}


function startProcess() {
  `nohup /data/srv/platformV2/platformV2 server -c=/data/srv/platformV2/config/settings_prod.yml >> /data/srv/platformV2/run.log 2>&1 &`
}

param=$1

if [ "$param" = "$pStart" ]
then
  echo "启动"
  start
elif [ "$param" = "$pStop" ]
then
  echo "停止"
  stopProcess
elif [ "$param" = "$pRestart" ]
then
  stopProcess
  sleep 1
  start
  echo "重启"
else
  echo "请输入 start、stop、restart中的一个"
  exit 1
fi

#isProcessRun
#isRun=$?
#echo $isRun
#
#startProcess









