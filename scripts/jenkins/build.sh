#!/bin/bash
#set -o errexit

#是否创建新Tag
isNew="1"
realTag=$TagName
if [ ! -z $BUILD_TAG ]
then
    isNew="0"
    realTag=$BUILD_TAG
fi


echo "IsNew:$isNew realTag:$realTag"
echo $realTag > tagfile
echo 开始进行tag检查


m=`egrep "^v-[0-9][0-9](0[1-9]|1[0-2])(0[1-9]|[12]{1}[0-9]|3[0-1])([01]{1}[0-9]|2[0-3])([0-5][0-9])$" ./tagfile`
echo "tag检查结果:"$m
if [ -z $m ]
then
  echo "tag规则检查不通过 tageName:$realTag  eg:v-2212271541"
  exit 1
else
  echo "tag规则检查通过 tageName:$realTag"
fi


#编译
go env -w GOPROXY=https://goproxy.cn,direct
go mod tidy



go build -o platformV2


echo 编译完成 $realTag

git tag $realTag
git remote set-url origin http://andy%40playsparkle.com:linxiaojieIi1@************/Liuniu/MT_Platform_v2.git
#git push origin develop
git push  origin $realTag


#打包
tar -zcvf  platformV2-$realTag.tar.gz  ./platformV2 ./config/settings_prod.yml





#上传到s3
chmod a+x ./scripts/s3/s3_upload.sh

./scripts/s3/s3_upload.sh ./platformV2-$realTag.tar.gz server/tar/platformV2/platformV2-$realTag.tar.gz

#set +o errexit

