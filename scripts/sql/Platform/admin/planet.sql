--
CREATE TABLE `t_planet` (
                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'planet_id',
                            `product_id` varchar(32) NOT NULL COMMENT 'product_id',
                            `name` varchar(32) DEFAULT NULL COMMENT 'planet名称',
                            `alias_name` varchar(32) DEFAULT NULL COMMENT '别名或简称',
                            `relation_name` varchar(32) DEFAULT NULL COMMENT '关联名称',
                            `describe` varchar(255) DEFAULT NULL COMMENT '描述',
                            `is_inland` tinyint DEFAULT NULL COMMENT '是否内地(1-是,2-海外)',
                            `status` tinyint DEFAULT NULL COMMENT '是否有效，1：是，0：否',
                            `app_id` varchar(255) DEFAULT NULL COMMENT 'appid',
                            `app_security` varchar(255) DEFAULT NULL COMMENT 'app_security',
                            `create_by` varchar(20) DEFAULT NULL COMMENT '创建者',
                            `update_by` varchar(20) DEFAULT NULL COMMENT '更新者',
                            `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
                            `updated_at` datetime(3) DEFAULT NULL COMMENT '最后更新时间',
                            `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;

-- ) ENGINE=InnoDB AUTO_INCREMENT = 1001  DEFAULT CHARSET=utf8mb4;


-- sparkleplt.t_env definition
CREATE TABLE `t_env` (
                         `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'env_id',
                         `planet_id` varchar(32) NOT NULL COMMENT 'planet id',
                         `env_type` int(11) NOT NULL COMMENT '对应env_type字段',
                         `name` varchar(32) NOT NULL,
                         `zh_name` varchar(32) NOT NULL,
                         `timezone` varchar(32) DEFAULT NULL COMMENT '时区',
                         `cloud` varchar(10) DEFAULT NULL COMMENT '所在云服务',
                         `host` varchar(255) DEFAULT NULL COMMENT 'host',
                         `ip` varchar(32) DEFAULT NULL COMMENT 'ip',
                         `port` int DEFAULT NULL COMMENT 'port',
                         `gm_addr` varchar(255) DEFAULT NULL COMMENT 'gm_addr',
                         `ck_addr` varchar(255) DEFAULT NULL COMMENT 'ck_addr',
                         `zk_addr` varchar(255) DEFAULT NULL COMMENT 'zk_addr',
                         `game_sql_addr` varchar(255) DEFAULT NULL COMMENT 'game_sql_addr',
                         `created_at` timestamp NULL DEFAULT NULL,
                         `updated_at` timestamp NULL DEFAULT NULL,
                         `create_by` bigint(20) NOT NULL,
                         `update_by` bigint(20) NOT NULL,
                         `deleted_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


-- 项目表
CREATE TABLE `t_project` (
                             `id` int NOT NULL AUTO_INCREMENT COMMENT '项目ID',
                             `product_id` varchar(32) NOT NULL COMMENT 'product_id',
                             `name` varchar(32) NOT NULL COMMENT '应用名称',
                             `describe` varchar(32) NOT NULL COMMENT '项目描述',
                             `leader` varchar(32) NOT NULL COMMENT '负责人邮箱或联系方式',
                             `subsystem` varchar(120) NOT NULL COMMENT '哪些子系统在使用(用逗号分隔)',
                             `status` tinyint NOT NULL COMMENT '是否有效，1：是，0：否',
                             `create_by` varchar(20) DEFAULT NULL COMMENT '创建者',
                             `update_by` varchar(20) DEFAULT NULL COMMENT '更新者',
                             `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
                             `updated_at` datetime(3) DEFAULT NULL COMMENT '最后更新时间',
                             `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
                             PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;


-- planet role表
CREATE TABLE `t_planet_role` (
                                 `id` int NOT NULL AUTO_INCREMENT COMMENT '项目ID',
                                 `role_id` int NOT NULL COMMENT 'role_id',
                                 `planet_id` int NOT NULL COMMENT 'planet_id',
                                 `env` int(11) NOT NULL COMMENT '系统环境ID',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;




