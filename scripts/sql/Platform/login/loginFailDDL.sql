-- sparklegen.t_fb_login_failed definition

create table `t_fb_login_failed` (
	`id` bigint NOT NULL AUTO_INCREMENT,
  	`Platform` int default null COMMENT 'Platform',
	`NowDate` datetime(3) default null COMMENT 'NowDate',
	`TimeStampValue` datetime(3) default null COMMENT 'TimeStampValue',
	`AccType` int default null COMMENT 'AccType',
	`Channel` int default null COMMENT 'Channel',
	`AppVersion` varchar(20) default null COMMENT 'AppVersion',
	`AppLanguage` varchar(20) default null COMMENT 'AppLanguage',
	`Country` varchar(20) default null COMMENT 'Country',
	`DeviceID` varchar(100) default null COMMENT 'DeviceID',
	`Reason` varchar(255) default null COMMENT 'Reason',
	  PRIMARY KEY (`id`)
) engine = InnoDB default CHARSET = utf8mb4 collate = utf8mb4_0900_ai_ci;