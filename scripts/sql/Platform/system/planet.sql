CREATE TABLE `t_planet` (
                            `pid` int NOT NULL COMMENT '项目ID',
                            `env` tinyint DEFAULT NULL COMMENT '0-开发, 1-测试, 2-预发布，3-生产环境',
                            `name` varchar(32) DEFAULT NULL COMMENT '平台名称',
                            `alias_name` varchar(32) DEFAULT NULL COMMENT '别名或简称',
                            `relation_name` varchar(32) DEFAULT NULL COMMENT '关联名称',
                            `describe` varchar(255) DEFAULT NULL COMMENT '描述',
                            `host` varchar(255) DEFAULT NULL COMMENT 'host',
                            `ip` varchar(32) DEFAULT NULL COMMENT 'ip',
                            `port` int DEFAULT NULL COMMENT 'port',
                            `is_inland` varchar(255) DEFAULT NULL COMMENT '是否内地(1-是,2-海外)',
                            `timezone` varchar(32) DEFAULT NULL COMMENT '时区',
                            `cloud` varchar(10) DEFAULT NULL COMMENT '所在云服务',
                            `status` tinyint DEFAULT NULL COMMENT '是否有效，1：是，0：否',
                            `gm_addr` varchar(255) DEFAULT NULL COMMENT 'gm_addr',
                            `app_id` varchar(255) DEFAULT NULL COMMENT 'appid',
                            `app_security` varchar(255) DEFAULT NULL COMMENT 'app_security',
                            `create_by` varchar(20) DEFAULT NULL COMMENT '创建者',
                            `update_by` varchar(20) DEFAULT NULL COMMENT '更新者',
                            `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
                            `updated_at` datetime(3) DEFAULT NULL COMMENT '最后更新时间',
                            `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
                            PRIMARY KEY (`pid`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4;