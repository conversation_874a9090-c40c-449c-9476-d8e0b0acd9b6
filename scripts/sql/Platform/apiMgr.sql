// �ͻ�����־
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(210, NULL, '�ͻ�����־-�б�', '/api/v1//t-client-log-list', '', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(211, NULL, '�ͻ�����־-����', '/api/v1//t-client-log-list/:id', '', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(212, NULL, '�ͻ�����־-���', '/api/v1//t-client-log-list', '', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(213, NULL, '�ͻ�����־-�޸�', '/api/v1//t-client-log-list/:id', '', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(214, NULL, '�ͻ�����־-ɾ��', '/api/v1//t-client-log-list/:id', '', 'DELETE', NULL, NULL, NULL, NULL, NULL);

// MockLogin
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(215, NULL, 'MockLogin-�б�', '/api/v1/t-mock-login', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(216, NULL, 'MockLogin-����', '/api/v1/t-mock-login/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(217, NULL, 'MockLogin-���', '/api/v1/t-mock-login', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(218, NULL, 'MockLogin-�޸�', '/api/v1/t-mock-login/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(219, NULL, 'MockLogin-ɾ��', '/api/v1/t-mock-login/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);

// PushLog
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(220, NULL, 't-push-log-�б�', '/api/v1/t-push-log', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(221, NULL, 't-push-log-����', '/api/v1/t-push-log/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(222, NULL, 't-push-log-���', '/api/v1/t-push-log', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(223, NULL, 't-push-log-�޸�', '/api/v1/t-push-log/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(224, NULL, 't-push-log-ɾ��', '/api/v1/t-push-log/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);



// Mail /api/v1/t-mail-master/1
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(225, NULL, 't-mail-master-�б�', '/api/v1/t-mail-master', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(226, NULL, 't-mail-master-����', '/api/v1/t-mail-master/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(227, NULL, 't-mail-master-���', '/api/v1/t-mail-master', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(228, NULL, 't-mail-master-�޸�', '/api/v1/t-mail-master/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(229, NULL, 't-mail-master-ɾ��', '/api/v1/t-mail-master', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);


// ClientLog t-client-log-list
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(230, NULL, 't-client-log-list-�б�', '/api/v1/t-client-log-list', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(231, NULL, 't-client-log-list-����', '/api/v1/t-client-log-list/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(232, NULL, 't-client-log-list-���', '/api/v1/t-client-log-list', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(233, NULL, 't-client-log-list-�޸�', '/api/v1/t-client-log-list/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(234, NULL, 't-client-log-list-ɾ��', '/api/v1/t-client-log-list/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);

INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(235, NULL, 't-client-log-list-��ѯS3��־', '/api/v1/t-client-log-down-list', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);


// ���ü�� field
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(236, NULL, '���ü��-field-�б�', '/api/v1/config-field-master', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(237, NULL, '���ü��-field-����', '/api/v1/config-field-master/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(238, NULL, '���ü��-field-���', '/api/v1/config-field-master', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(239, NULL, '���ü��-field-�޸�', '/api/v1/config-field-master/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(240, NULL, '���ü��-field-ɾ��', '/api/v1/config-field-master/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);

// ���ü�� check
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(241, NULL, '���ü��-check-�б�', '/api/v1/config-check-master', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(242, NULL, '���ü��-check-����', '/api/v1/config-check-master/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(243, NULL, '���ü��-check-���', '/api/v1/config-check-master', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(244, NULL, '���ü��-check-�޸�', '/api/v1/config-check-master/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(245, NULL, '���ü��-check-ɾ��', '/api/v1/config-check-master/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);

// ���ü�� rule
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(246, NULL, '���ü��-rule-�б�', '/api/v1/config-rule-master', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(247, NULL, '���ü��-rule-����', '/api/v1/config-rule-master/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(248, NULL, '���ü��-rule-���', '/api/v1/config-rule-master', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(249, NULL, '���ü��-rule-�޸�', '/api/v1/config-rule-master/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(250, NULL, '���ü��-rule-ɾ��', '/api/v1/config-rule-master/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);


// ���ü�� foreign-key
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(251, NULL, '���ü��-foreign-key-�б�', '/api/v1/config-foreign-key-master', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(252, NULL, '���ü��-foreign-key-����', '/api/v1/config-foreign-key-master/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(253, NULL, '���ü��-foreign-key-���', '/api/v1/config-foreign-key-master', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(254, NULL, '���ü��-foreign-key-�޸�', '/api/v1/config-foreign-key-master/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(255, NULL, '���ü��-foreign-key-ɾ��', '/api/v1/config-foreign-key-master/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);


// RFM t-rfm-master
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(256, NULL, 'RFM����-t-rfm-master-�б�', '/api/v1/t-rfm-master', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(257, NULL, 'RFM����-t-rfm-master-����', '/api/v1/t-rfm-master/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(258, NULL, 'RFM����-t-rfm-master-���', '/api/v1/t-rfm-master', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(259, NULL, 'RFM����-t-rfm-master-�޸�', '/api/v1/t-rfm-master/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(260, NULL, 'RFM����-t-rfm-master-ɾ��', '/api/v1/t-rfm-master/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);

// ���ü�鵼�����������
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(261, NULL, '���ü��-foreign-key-����', '/api/v1/config-foreign-key-master-excel', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);

// ���ü��field ���룬����
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(262, NULL, '���ü��-field--����', '/api/v1/config-field-master-excel', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);


// t-fb-login-failed
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(263, NULL, 't-fb-login-failed-�б�', '/api/v1/t-fb-login-failed', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(264, NULL, 't-fb-login-failed-����', '/api/v1/t-fb-login-failed-Group', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(265, NULL, 't-fb-login-failed-����', '/api/v1/t-fb-login-failed/:id', 'BUS', 'GET', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(266, NULL, 't-fb-login-failed-���', '/api/v1/t-fb-login-failed', 'BUS', 'POST', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(267, NULL, 't-fb-login-failed-�޸�', '/api/v1/t-fb-login-failed/:id', 'BUS', 'PUT', NULL, NULL, NULL, NULL, NULL);
INSERT INTO sparkleplt.sys_api
(id, handle, title, `path`, `type`, `action`, created_at, updated_at, deleted_at, create_by, update_by)
VALUES(268, NULL, 't-fb-login-failed-ɾ��', '/api/v1/t-fb-login-failed/:id', 'BUS', 'DELETE', NULL, NULL, NULL, NULL, NULL);



