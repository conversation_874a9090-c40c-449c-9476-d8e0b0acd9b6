package models

import (
	"encoding/json"
	"time"
)

// CDKBatch CDK批次
type CDKBatch struct {
	Id          uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                         // 批次ID
	ChannelId   int32  `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3,enum=common.CHANNEL_TYPE" json:"channel_id,omitempty"` // 渠道ID
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                                        // 批次描述
	CdkCount    int32  `protobuf:"varint,4,opt,name=cdk_count,json=cdkCount,proto3" json:"cdk_count,omitempty"`                             // CDK数量
	Status      int32  `protobuf:"varint,5,opt,name=status,proto3,enum=common.CDK_BATCH_STATUS" json:"status,omitempty"`                    // 状态
	CreatedAt   int64  `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                          // 创建时间 (Unix时间戳)
	UpdatedAt   int64  `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                          // 更新时间 (Unix时间戳)
}

// CDKRecord CDK记录
type CDKRecord struct {
	ID        int64      `json:"id" gorm:"primaryKey;autoIncrement"`
	BatchID   int64      `json:"batch_id" gorm:"not null;index"`
	CDKCode   string     `json:"cdk_code" gorm:"size:50;not null;uniqueIndex"`
	Status    int32      `json:"status" gorm:"default:1;comment:1=未使用,2=已使用,3=已过期"`
	PlayerID  int64      `json:"player_id" gorm:"default:0;comment:使用者ID"`
	UsedAt    *time.Time `json:"used_at" gorm:"comment:使用时间"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// 使用已存在的ApiResponse结构

// CDKBatchData GM返回的CDK批次数据
type CDKBatchData struct {
	Batches []*CDKBatch `json:"batches"`
	Total   int64       `json:"total"`
}

// CDKRecordData GM返回的CDK记录数据
type CDKRecordData struct {
	Records []*CDKRecord `json:"records"`
	Total   int64        `json:"total"`
}

// ParseCDKBatchResponse 解析CDK批次查询响应
func ParseCDKBatchResponse(jsonData string) ([]*CDKBatch, int64, error) {
	var apiResp ApiResponse
	if err := json.Unmarshal([]byte(jsonData), &apiResp); err != nil {
		return nil, 0, err
	}

	var batchData CDKBatchData
	if err := json.Unmarshal([]byte(apiResp.Data.Data), &batchData); err != nil {
		return nil, 0, err
	}

	return batchData.Batches, batchData.Total, nil
}

// ParseCDKRecordResponse 解析CDK记录查询响应
func ParseCDKRecordResponse(jsonData string) ([]*CDKRecord, int64, error) {
	var apiResp ApiResponse
	if err := json.Unmarshal([]byte(jsonData), &apiResp); err != nil {
		return nil, 0, err
	}

	var recordData CDKRecordData
	if err := json.Unmarshal([]byte(apiResp.Data.Data), &recordData); err != nil {
		return nil, 0, err
	}

	return recordData.Records, recordData.Total, nil
}
