package dto

import "go-admin/common/dto"

// CreateCDKBatchReq 创建CDK批次请求
type CreateCDKBatchReq struct {
	BatchName        string   `json:"batch_name" binding:"required" comment:"批次名称"`
	Description      string   `json:"description" comment:"批次描述"`
	StartTime        int64    `json:"start_time" binding:"required" comment:"开始时间"`
	EndTime          int64    `json:"end_time" binding:"required" comment:"结束时间"`
	Rewards          string   `json:"rewards" binding:"required" comment:"奖励配置JSON"`
	GenerationOption int32    `json:"generation_option" binding:"required" comment:"生成选项:1=随机生成,2=手动输入"`
	GenerationCount  int32    `json:"generation_count" comment:"随机生成数量"`
	ManualCDKs       []string `json:"manual_cdks" comment:"手动输入的CDK列表"`
}

// QueryCDKBatchesReq CDK批次查询请求
type QueryCDKBatchesReq struct {
	dto.Pagination
	Status int32 `form:"status" comment:"状态筛选:1=有效,2=无效"`
}

// QueryCDKRecordsReq CDK记录查询请求
type QueryCDKRecordsReq struct {
	dto.Pagination
	BatchID int64 `form:"batch_id" binding:"required" comment:"批次ID"`
}

// DisableCDKBatchReq CDK批次作废请求
type DisableCDKBatchReq struct {
	BatchID int64 `json:"batch_id" binding:"required" comment:"批次ID"`
}
